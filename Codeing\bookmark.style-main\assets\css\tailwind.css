@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;

    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
 
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
 
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
 
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
 
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
 
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
 
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
 
    --radius: 0.5rem;
 
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;

    /* Gradient Colors */
    --gradient-1-from: 142 76% 61%;
    --gradient-1-via: 217 91% 60%;
    --gradient-1-to: 271 91% 65%;
    
    --gradient-2-from: 330 100% 50%;
    --gradient-2-via: 0 100% 50%;
    --gradient-2-to: 60 100% 50%;
    
    --gradient-3-from: 330 100% 71%;
    --gradient-3-via: 271 100% 71%;
    --gradient-3-to: 217 100% 71%;
    
    --gradient-4-from: 217 100% 71%;
    --gradient-4-via: 0 100% 71%;
    --gradient-4-to: 60 100% 71%;
    
    --gradient-5-from: 0 100% 71%;
    --gradient-5-via: 330 100% 71%;
    --gradient-5-to: 60 100% 71%;
    
    --gradient-6-from: 199 100% 50%;
    --gradient-6-via: 330 100% 50%;
    --gradient-6-to: 120 100% 50%;
    
    --gradient-7-from: 0 0% 43%;
    --gradient-7-via: 0 0% 9%;
    --gradient-7-to: 0 0% 0%;
    
    --gradient-8-from: 0 0% 9%;
    --gradient-8-to: 0 0% 43%;
    
    --gradient-9-from: 0 0% 71%;
    --gradient-9-via: 0 0% 43%;
    --gradient-9-to: 0 0% 29%;
    
    --gradient-10-from: 217 91% 60%;
    --gradient-10-via: 217 91% 53%;
    --gradient-10-to: 0 0% 9%;
    
    --gradient-11-from: 199 100% 50%;
    --gradient-11-to: 217 91% 60%;
    
    --gradient-12-from: 160 100% 71%;
    --gradient-12-to: 120 100% 71%;
  }
 
  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
 
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
 
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
 
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
 
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
 
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
 
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
 
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
 
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}
 
@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }

  .text-neon {
    @apply text-transparent bg-clip-text bg-gradient-to-r from-sky-500 to-sky-900 dark:to-sky-200;
  }
  
  .glass {
    @apply backdrop-blur-lg backdrop-saturate-[180%] drop-shadow-xl;
  }
  
  .btn {
    @apply font-semibold h-12 px-6 rounded-lg inline-flex items-center justify-center sm:w-auto;
  }
  
  .btn-primary {
    @apply bg-sky-500 text-white border border-neutral-200/30 dark:bg-sky-500 dark:text-neutral-200 hover:bg-sky-400 hover:border-opacity-10 dark:hover:bg-opacity-10 focus:outline-none focus:ring-2 focus:ring-neutral-400 focus:ring-offset-2 focus:ring-offset-neutral-50;
  }
  
  .btn-secondary {
    @apply bg-neutral-200 text-neutral-800 border border-neutral-200/30 dark:bg-white dark:text-neutral-200 hover:bg-sky-400 hover:text-white hover:border-opacity-10 dark:hover:bg-opacity-10 focus:outline-none focus:ring-2 focus:ring-neutral-400 focus:ring-offset-2 focus:ring-offset-neutral-50;
  }
  
  .btn-blur {
    @apply backdrop-blur-sm backdrop-saturate-[180%] dark:bg-opacity-30;
  }
  
  .btn-icon {
    @apply p-2 hover:bg-neutral-200 dark:hover:bg-neutral-800/50 text-neutral-900 dark:text-neutral-200 rounded-md outline-none transition-all duration-200 ease-in-out;
  }
  
  .card-blur {
    @apply backdrop-blur-lg backdrop-saturate-[180%];
  }

  /* Gradient Classes */
  .gradient-1 {
    background-image: linear-gradient(to var(--gradient-angle, bottom right), 
      hsl(var(--gradient-1-from)), 
      hsl(var(--gradient-1-via)), 
      hsl(var(--gradient-1-to)));
  }

  .gradient-2 {
    background-image: linear-gradient(to var(--gradient-angle, bottom right), 
      hsl(var(--gradient-2-from)), 
      hsl(var(--gradient-2-via)), 
      hsl(var(--gradient-2-to)));
  }

  .gradient-3 {
    background-image: linear-gradient(to var(--gradient-angle, bottom right), 
      hsl(var(--gradient-3-from)), 
      hsl(var(--gradient-3-via)), 
      hsl(var(--gradient-3-to)));
  }

  .gradient-4 {
    background-image: linear-gradient(to var(--gradient-angle, bottom right), 
      hsl(var(--gradient-4-from)), 
      hsl(var(--gradient-4-via)), 
      hsl(var(--gradient-4-to)));
  }

  .gradient-5 {
    background-image: linear-gradient(to var(--gradient-angle, bottom right), 
      hsl(var(--gradient-5-from)), 
      hsl(var(--gradient-5-via)), 
      hsl(var(--gradient-5-to)));
  }

  .gradient-6 {
    background-image: linear-gradient(to var(--gradient-angle, bottom right), 
      hsl(var(--gradient-6-from)), 
      hsl(var(--gradient-6-via)), 
      hsl(var(--gradient-6-to)));
  }

  .gradient-7 {
    background-image: linear-gradient(to var(--gradient-angle, bottom right), 
      hsl(var(--gradient-7-from)), 
      hsl(var(--gradient-7-via)), 
      hsl(var(--gradient-7-to)));
  }

  .gradient-8 {
    background-image: linear-gradient(to var(--gradient-angle, bottom right), 
      hsl(var(--gradient-8-from)), 
      hsl(var(--gradient-8-to)));
  }

  .gradient-9 {
    background-image: linear-gradient(to var(--gradient-angle, bottom right), 
      hsl(var(--gradient-9-from)), 
      hsl(var(--gradient-9-via)), 
      hsl(var(--gradient-9-to)));
  }

  .gradient-10 {
    background-image: linear-gradient(to var(--gradient-angle, bottom right), 
      hsl(var(--gradient-10-from)), 
      hsl(var(--gradient-10-via)), 
      hsl(var(--gradient-10-to)));
  }

  .gradient-11 {
    background-image: linear-gradient(to var(--gradient-angle, bottom right), 
      hsl(var(--gradient-11-from)), 
      hsl(var(--gradient-11-to)));
  }

  .gradient-12 {
    background-image: linear-gradient(to var(--gradient-angle, bottom right), 
      hsl(var(--gradient-12-from)), 
      hsl(var(--gradient-12-to)));
  }
} 