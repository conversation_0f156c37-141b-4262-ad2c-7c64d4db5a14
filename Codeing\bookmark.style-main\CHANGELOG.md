# [1.0.0](https://github.com/one-tab-group/bookmark.style/compare/v0.3.0...v1.0.0) (2022-11-28)


### Features

* rename the service ([ebcdf84](https://github.com/one-tab-group/bookmark.style/commit/ebcdf840e19a20f844ec89c50edbca7adf6cab1f))



# [0.3.0](https://github.com/one-tab-group/bookmark.style/compare/v0.2.0...v0.3.0) (2022-10-07)


### Bug Fixes

* fixed the cursor pointer of product hunt icon ([98167ac](https://github.com/one-tab-group/bookmark.style/commit/98167ac0a50b429c9771100f002a26a9dacdfb3e))
* fixed the link from chrome extension ([8e01546](https://github.com/one-tab-group/bookmark.style/commit/8e01546aca474f33ebe2c73751ba6f133a59cf22))
* fixed the link from chrome extension ([a72aa8b](https://github.com/one-tab-group/bookmark.style/commit/a72aa8b1098a9b1eb1d144ac5815a6706b2619da))


### Features

* add the copy to README.md feature ([4e5abb5](https://github.com/one-tab-group/bookmark.style/commit/4e5abb555630ea2c5b17f44e434f56f7b7109d4c))
* move the product hunt badge to the left-bottom ([1db3db6](https://github.com/one-tab-group/bookmark.style/commit/1db3db66f392946dec4de1f9c9968a6494efe3a1))
* use the umami as the analytics solution ([f948683](https://github.com/one-tab-group/bookmark.style/commit/f94868359e3e645847c13756a1fdd339e7ff78ab))



# [0.2.0](https://github.com/one-tab-group/bookmark.style/compare/v0.1.0...v0.2.0) (2022-04-29)


### Bug Fixes

* add missing file ([f200763](https://github.com/one-tab-group/bookmark.style/commit/f2007638757aea7c25fccce34570bcb3c315653a))


### Features

* add netlify file ([fb7a8fc](https://github.com/one-tab-group/bookmark.style/commit/fb7a8fc9ab6f2b826b3c8f6aba7ab305df589ce7))
* add parse the url search params feature ([c31d21b](https://github.com/one-tab-group/bookmark.style/commit/c31d21bdc94dff0e5a0ac1c5f239fab29c4b7f5a))
* add producthunt badge ([a9a316f](https://github.com/one-tab-group/bookmark.style/commit/a9a316fe715ba4676acc65a335c5415dc904b0bb))
* add sponsor me on github link ([0e49047](https://github.com/one-tab-group/bookmark.style/commit/0e49047dbe71af8200699d53692ac17b461cda52))
* add the copyright ([3067726](https://github.com/one-tab-group/bookmark.style/commit/30677263006783775a08e86bf4e245430091c4ff))
* add the meta image ([ec58c19](https://github.com/one-tab-group/bookmark.style/commit/ec58c19063824df5fccfd9e46e3e874fa228ddc1))
* fetch meta data in the backend ([b343325](https://github.com/one-tab-group/bookmark.style/commit/b343325bd70a40054ecb3710426489a4e563f497))
* improve the footer style ([04ce39e](https://github.com/one-tab-group/bookmark.style/commit/04ce39e873bd361f4954d6c140e28324a06ed5b9))
* make visual-bookmark is a client-only component ([5d1d1e4](https://github.com/one-tab-group/bookmark.style/commit/5d1d1e44fed79198b47801727da7ad880ba69762))
* modify the default link ([f64a7c2](https://github.com/one-tab-group/bookmark.style/commit/f64a7c26788adfcd12b0a63fd611d0ca9daf8820))



# [0.1.0](https://github.com/one-tab-group/bookmark.style/compare/ec0cbbdef0b81240e58d03a3405eb1705687eebe...v0.1.0) (2022-04-10)


### Features

* add meta info ([976d12b](https://github.com/one-tab-group/bookmark.style/commit/976d12b56c0062da54efb975623d7431719f696d))
* add plausible support ([ec0cbbd](https://github.com/one-tab-group/bookmark.style/commit/ec0cbbdef0b81240e58d03a3405eb1705687eebe))
* add the logo ([14f6888](https://github.com/one-tab-group/bookmark.style/commit/14f68881da4a478e92bf7cca2d1c9f216e343ca9))
* replace preview image ([5f8d8c4](https://github.com/one-tab-group/bookmark.style/commit/5f8d8c46a1b25e6241529a4e91b230c6b5cd17f7))



