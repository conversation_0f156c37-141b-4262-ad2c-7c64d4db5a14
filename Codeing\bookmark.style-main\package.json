{"name": "bookmark.style", "displayName": "Bookmark Style", "description": "🪄 Turn any link into a stylish web visual bookmark, one-click to copy the beautiful web bookmark image.", "author": "xiaoluoboding <<EMAIL>>", "version": "1.0.0", "scripts": {"dev": "nuxi dev --port 3099", "build": "nuxi build", "start": "node .output/server/index.mjs", "preview": "nuxi preview", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s -r 0", "p:ish": "pnpm install --shamefully-hoist", "p:up": "pnpm up"}, "repository": {"type": "git", "url": "git+https://github.com/one-tab-group/bookmark.style"}, "homepage": "https://bookmark.style", "devDependencies": {"@iconify/json": "^2.2.322", "@nuxtjs/tailwindcss": "^6.13.2", "@pinia/nuxt": "^0.4.11", "@types/axios": "^0.14.4", "@types/dom-to-image": "^2.6.7", "@types/node": "^22.13.14", "@vueuse/components": "^9.13.0", "@vueuse/core": "9.6.0", "@vueuse/nuxt": "9.6.0", "autoprefixer": "^10.4.17", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "nuxt": "^3.16.1", "postcss": "^8.4.35", "shadcn-vue": "^0.8.0", "tailwind-merge": "^2.6.0", "tailwindcss": "^3.4.1", "tailwindcss-animate": "^1.0.7", "unplugin-icons": "^0.14.15", "unplugin-vue-components": "^0.22.12", "vue": "^3.5.13"}, "dependencies": {"axios": "^1.8.4", "copy-image-clipboard": "^2.1.2", "dom-to-image": "^2.6.0", "fancy-qrcode": "^0.1.0", "file-saver": "^2.0.5", "html2canvas": "^1.4.1", "lucide-vue-next": "^0.486.0", "pinia": "^2.3.1", "radix-vue": "^1.5.3", "reka-ui": "^2.1.1", "vue-sonner": "^1.3.0"}}