// generated by unplugin-vue-components
// We suggest you to commit this file into source control
// Read more: https://github.com/vuejs/core/pull/3399
import '@vue/runtime-core'

export {}

declare module '@vue/runtime-core' {
  export interface GlobalComponents {
    'Carbon:cafe': typeof import('~icons/carbon/cafe')['default']
    'Carbon:copy': typeof import('~icons/carbon/copy')['default']
    'Carbon:download': typeof import('~icons/carbon/download')['default']
    'Carbon:imageCopy': typeof import('~icons/carbon/image-copy')['default']
    'Carbon:logoTwitter': typeof import('~icons/carbon/logo-twitter')['default']
    'Carbon:search': typeof import('~icons/carbon/search')['default']
    CarbonLogoGithub: typeof import('~icons/carbon/logo-github')['default']
    CarbonMoon: typeof import('~icons/carbon/moon')['default']
    CarbonSun: typeof import('~icons/carbon/sun')['default']
    'HeroiconsOutline:checkCircle': typeof import('~icons/heroicons-outline/check-circle')['default']
    'HeroiconsOutline:exclamation': typeof import('~icons/heroicons-outline/exclamation')['default']
    'HeroiconsOutline:exclamationCircle': typeof import('~icons/heroicons-outline/exclamation-circle')['default']
    'HeroiconsOutline:informationCircle': typeof import('~icons/heroicons-outline/information-circle')['default']
    'HeroiconsSolid:check': typeof import('~icons/heroicons-solid/check')['default']
    'HeroiconsSolid:selector': typeof import('~icons/heroicons-solid/selector')['default']
    'HeroiconsSolid:x': typeof import('~icons/heroicons-solid/x')['default']
    'Mdi:circle': typeof import('~icons/mdi/circle')['default']
    'Mdi:heart': typeof import('~icons/mdi/heart')['default']
    'Mdi:qrcodeMinus': typeof import('~icons/mdi/qrcode-minus')['default']
    'Mdi:qrcodePlus': typeof import('~icons/mdi/qrcode-plus')['default']
    'Mdi:twitter': typeof import('~icons/mdi/twitter')['default']
    MdiDockRight: typeof import('~icons/mdi/dock-right')['default']
    MdiDockTop: typeof import('~icons/mdi/dock-top')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
  }
}
